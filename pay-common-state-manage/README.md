# 通用状态管理SDK

## 概述

pay-common-state-manage是一个通用状态管理SDK，通过调用StateService能实现管理业务状态，同时维护一个总的业务状态和多个子状态。该SDK实现了"只有当所有子状态都为true时，总状态才为true"的逻辑，并提供了扩展机制允许不同业务类型定义自己的状态处理逻辑。

## 架构设计

### 核心组件

1. **StateService**：状态服务接口，定义了状态的查询和更新方法
2. **StateServiceImpl**：状态服务实现类，提供通用的状态管理逻辑
3. **StateProcessor**：状态处理器接口，允许不同业务类型自定义状态处理逻辑
4. **StateProcessorRegistry**：状态处理器注册表，管理所有注册的处理器
5. **CommonStateEntity**：状态实体类，直接存储业务数据字段

### 状态管理逻辑

- 每个业务实体维护一个总状态和多个子状态
- 只有当所有子状态都为true时，总状态才为true
- 业务必须通过实现StateProcessor接口定义自己的状态处理逻辑

### 设计原则

- **不使用Map传输数据**：SDK直接使用具体的字段存储业务数据，避免使用Map传输数据带来的类型不安全问题
- **强制使用处理器**：SDK强制要求每种业务类型都必须实现自己的处理器，不提供默认实现

### 扩展机制

SDK通过StateProcessor接口提供扩展机制，允许不同业务类型自定义：
- 实体查找和创建逻辑
- 结果实例创建逻辑
- 结果字段设置逻辑
- 状态更新前后处理逻辑
- 总状态计算逻辑

## 使用示例

### 基本用法

```java
// 注入StateService
@Autowired
private StateService stateService;

// 查询状态
TradeStateQueryRequest queryRequest = new TradeStateQueryRequest();
queryRequest.setBiz("trade");
queryRequest.setTradeId("T123456");
queryRequest.setMerchantId("M123456");

TradeStateResult result = stateService.queryState(queryRequest);
boolean overallState = result.getState(); // 获取总状态

// 更新状态
TradeStateChangeRequest changeRequest = new TradeStateChangeRequest();
changeRequest.setBiz("trade");
changeRequest.setTradeId("T123456");
changeRequest.setMerchantId("M123456");
changeRequest.setType(1); // 子状态类型
changeRequest.setOpen(true); // 设置子状态值
changeRequest.setRemark("支付成功");

OpLogCreateRequest logRequest = new OpLogCreateRequest();
logRequest.setOperator("admin");

stateService.updateState(changeRequest, logRequest);
```

### 自定义业务处理器

```java
public class CustomBusinessProcessor implements StateProcessor<CustomStateBean, CustomStateResult> {
    
    @Override
    public String getSupportedBizType() {
        return "custom_biz";
    }
    
    @Override
    public CommonStateEntity findOrCreateEntity(CustomStateBean stateBean, CommonStateDao commonStateDao, boolean isCreate) {
        // 查找业务实体
        String customId = stateBean.getCustomId();
        CommonStateEntity entity = commonStateDao.findByCustomId(customId);
        
        // 如果需要创建且未找到实体，则创建新实体
        if (entity == null && isCreate) {
            entity = new CommonStateEntity();
            entity.setBiz(stateBean.getBiz());
            entity.setCustomId(customId);
            // 设置其他必要字段
        }
        
        return entity;
    }
    
    @Override
    public CustomStateResult createResultInstance(CustomStateBean stateBean) {
        return new CustomStateResult();
    }
    
    @Override
    public void setResultFields(CustomStateResult result, CommonStateEntity entity) {
        result.setCustomId(entity.getCustomId());
        // 设置其他字段...
    }
    
    @Override
    public boolean beforeUpdate(CustomStateBean updateStateBean, CommonStateEntity entity) {
        // 业务特定的前置检查逻辑
        return true;
    }
    
    @Override
    public void afterUpdate(CustomStateBean updateStateBean, CommonStateEntity entity, boolean success) {
        // 业务特定的后置处理逻辑
    }
    
    @Override
    public boolean calculateOverallState(Map<Integer, Boolean> subStates) {
        // 自定义总状态计算逻辑
        // 例如：至少有一个子状态为true，总状态就为true
        for (Boolean value : subStates.values()) {
            if (value != null && value) {
                return true;
            }
        }
        return false;
    }
}
```

### 注册自定义处理器

```java
@Configuration
public class StateManageConfig {
    
    @Autowired
    private StateServiceImpl stateService;
    
    @PostConstruct
    public void init() {
        // 注册自定义业务处理器
        stateService.registerProcessor(new CustomBusinessProcessor());
    }
}
```

## 注意事项

1. 使用SDK前必须确保已正确配置数据库和相关表结构
2. 每个业务类型需要预先在状态配置表中注册
3. **必须为每种业务类型实现处理器**，SDK不提供默认实现
4. 处理器的getSupportedBizType()方法返回值必须与配置中的业务类型一致
5. 如需修改"所有子状态都为true时，总状态才为true"的逻辑，请在实现处理器时覆盖calculateOverallState()方法

## 配置定时刷新功能

StateConfig类提供了定时刷新配置的功能，可以自动从数据库加载最新的状态配置信息。以下是使用方法：

### 1. 创建StateConfig实例

```java
// 创建不启用定时刷新的实例
StateConfig stateConfig = new StateConfig(bizDisableReasonDao);

// 创建启用定时刷新的实例，并设置刷新间隔为60秒
StateConfig stateConfig = new StateConfig(bizDisableReasonDao, 60, true);
```

### 2. 控制定时刷新行为

```java
// 手动启动定时刷新
stateConfig.startScheduledRefresh();

// 停止定时刷新
stateConfig.stopScheduledRefresh();

// 设置刷新间隔（单位：秒）
stateConfig.setRefreshInterval(120); // 设置为2分钟

// 启用或禁用自动刷新
stateConfig.setEnableAutoRefresh(true); // 启用
stateConfig.setEnableAutoRefresh(false); // 禁用

// 手动触发配置刷新
stateConfig.reload();
```

### 3. 在Spring配置中使用

在Spring配置中创建StateConfig bean，并配置定时刷新：

```java
@Configuration
public class StateManageConfig {
    
    @Bean
    public CommonStateDao commonStateDao(NamedParameterJdbcTemplate jdbcTemplate) {
        return new CommonStateDao(jdbcTemplate);
    }
    
    @Bean
    public BizDisableReasonDao bizDisableReasonDao(NamedParameterJdbcTemplate jdbcTemplate) {
        return new BizDisableReasonDao(jdbcTemplate);
    }
    
    @Bean
    public StateConfig stateConfig(BizDisableReasonDao bizDisableReasonDao) {
        // 创建启用定时刷新的StateConfig，刷新间隔为5分钟
        return new StateConfig(bizDisableReasonDao, 300, true);
    }
    
    @Bean
    public StateService stateService(CommonStateDao commonStateDao, StateConfig stateConfig) {
        return new StateServiceImpl(commonStateDao);
    }
}
```

### 4. 在应用关闭时清理资源

为了确保应用优雅关闭，应在适当的时机调用`destroy()`方法停止定时任务：

```java
@PreDestroy
public void onApplicationShutdown() {
    stateConfig.destroy();
}
```

### 5. 最佳实践

- **选择合适的刷新间隔**：间隔太短会增加数据库负担，太长则可能导致配置更新不及时。建议根据业务场景设置，一般为5-15分钟。
- **考虑使用日志监控**：StateConfig中的日志记录可以帮助监控配置刷新情况，建议配置合适的日志级别。
- **应用启动时加载**：确保在应用启动时就完成首次配置加载，避免首次访问时的延迟。

## 数据库表结构

SDK依赖以下数据库表结构：

### common_trade_state表

```sql
CREATE TABLE `common_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `biz` varchar(32) NOT NULL COMMENT '业务类型',
  `biz_id` varchar(128) NOT NULL COMMENT '业务唯一标识',
  `state` tinyint(1) NOT NULL DEFAULT '0' COMMENT '总状态值：0-false，1-true',
  `sub_states_bits` varchar(64) DEFAULT NULL COMMENT '子状态位串，1表示正常，0表示关闭',
  `sub_state_remarks` json DEFAULT NULL COMMENT '子状态备注，格式：{type: remark}',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `biz_data` text DEFAULT NULL COMMENT '业务数据JSON，不同业务类型存储各自所需数据',
  `extra` json DEFAULT NULL COMMENT '额外字段',
  `ctime` bigint(20) NOT NULL COMMENT '创建时间',
  `mtime` bigint(20) NOT NULL COMMENT '更新时间',
  `version` int(11) NOT NULL DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_biz_bizid` (`biz`,`biz_id`),
  KEY `idx_biz_id` (`biz_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通用状态表';
```

### biz_disable_reason表

```sql
CREATE TABLE `biz_disable_reason` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `biz` varchar(32) NOT NULL COMMENT '业务类型',
  `type` int(11) NOT NULL COMMENT '禁用原因类型',
  `desc` varchar(255) NOT NULL COMMENT '禁用原因描述',
  `ctime` bigint(20) NOT NULL COMMENT '创建时间',
  `mtime` bigint(20) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_biz_type` (`biz`,`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='业务禁用原因表';
```

### 表结构说明

1. **common_state表**：
   - 存储所有业务类型的通用状态信息
   - `biz`和`biz_id`组合唯一标识一条业务记录
   - `state`字段存储总状态
   - `sub_states_bits`字段使用二进制字符串存储所有子状态，每位对应一个状态类型
   - `sub_state_remarks`字段存储子状态的备注信息
   - `biz_data`字段存储业务特定数据的JSON字符串
   - `extra`字段可存储额外的系统信息

2. **biz_disable_reason表**：
   - 配置各业务类型的禁用原因（对应子状态类型）
   - `biz`和`type`组合唯一
   - 用于状态管理系统初始化和展示

## Spring集成指南

由于SDK本身不依赖Spring框架，在Spring项目中使用时需要进行一些额外的配置。以下是将SDK集成到Spring项目中的详细步骤：

### 1. 创建配置类

在Spring项目中创建一个配置类，手动声明SDK中的关键Bean：

```java
@Configuration
public class StateManageConfig {

    /**
     * 创建CommonStateDao
     */
    @Bean
    public CommonStateDao commonStateDao(NamedParameterJdbcTemplate jdbcTemplate) {
        return new CommonStateDao(jdbcTemplate);
    }
    
    /**
     * 创建StateService
     */
    @Bean
    public StateService stateService(CommonStateDao commonStateDao) {
        return new StateServiceImpl(commonStateDao);
    }
    
    /**
     * 初始化并注册处理器
     */
    @Bean
    public void initStateProcessors(StateService stateService) {
        // 注册交易状态处理器
        stateService.registerProcessor(new TradeStateProcessor());
        
        // 注册其他业务处理器
        // stateService.registerProcessor(new YourCustomProcessor());
        
        // 或者批量注册
        /*
        List<StateProcessor<?, ?>> processors = Arrays.asList(
            new OrderStateProcessor(),
            new RefundStateProcessor()
        );
        stateService.registerProcessors(processors);
        */
    }
}
```

### 2. 配置数据源和JDBC Template

确保已经配置了数据源和NamedParameterJdbcTemplate：

```java
@Configuration
public class DatabaseConfig {

    @Bean
    public DataSource dataSource() {
        // 配置你的数据源
        return new HikariDataSource(/* 数据源配置 */);
    }

    @Bean
    public NamedParameterJdbcTemplate namedParameterJdbcTemplate(DataSource dataSource) {
        return new NamedParameterJdbcTemplate(dataSource);
    }
}
```

### 3. 使用StateService

现在可以在任何Spring组件中注入并使用StateService：

```java
@Service
public class TradeStateManagerImpl implements TradeStateManager {

    private final StateService stateService;
    
    @Autowired
    public TradeStateManagerImpl(StateService stateService) {
        this.stateService = stateService;
    }
    
    @Override
    public void updateTradeState(String tradeId, String merchantId, int type, boolean open) {
        TradeStateChangeRequest request = new TradeStateChangeRequest();
        request.setBiz("trade");
        request.setTradeId(tradeId);
        request.setMerchantId(merchantId);
        request.setType(type);
        request.setOpen(open);
        
        OpLogCreateRequest logRequest = new OpLogCreateRequest();
        logRequest.setOperator("system");
        
        stateService.updateState(request, logRequest);
    }
    
    @Override
    public TradeStateResult queryTradeState(String tradeId, String merchantId) {
        TradeStateQueryRequest request = new TradeStateQueryRequest();
        request.setBiz("trade");
        request.setTradeId(tradeId);
        request.setMerchantId(merchantId);
        
        return stateService.queryState(request);
    }
}
```

### 4. 多模块项目配置

在多模块项目中，确保包含SDK所在模块的依赖：

```xml
<dependency>
    <groupId>com.wosai.pay</groupId>
    <artifactId>pay-common-state-manage</artifactId>
    <version>${project.version}</version>
</dependency>
```

并配置组件扫描：

```java
@SpringBootApplication
@ComponentScan(basePackages = {
    "com.wosai.your.project.package",
    "com.wosai.pay.common.state.manage"  // 如果需要扫描SDK中的类
})
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
```

### 5. 故障排查

如果遇到Bean无法找到的问题（例如：`Field stateService in com.wosai.trade.statemanage.ProviderStateServiceImpl required a bean of type 'com.wosai.pay.common.state.manage.service.StateService' that could not be found.`），请检查：

1. 是否已创建配置类并正确声明StateService和CommonStateDao的Bean
2. 配置类是否被Spring正确扫描到
3. 是否存在循环依赖问题
4. Spring Boot自动配置是否与手动配置冲突

### 6. 自定义处理器注册

注册自定义处理器可以通过StateService接口直接完成，无需强制类型转换：

```java
@Configuration
public class StateProcessorConfig {
    
    @Bean
    public void registerProcessors(StateService stateService) {
        // 单个注册
        stateService.registerProcessor(new TradeStateProcessor());
        
        // 批量注册
        List<StateProcessor<?, ?>> processors = Arrays.asList(
            new OrderStateProcessor(),
            new RefundStateProcessor(),
            new PaymentStateProcessor()
        );
        
        int count = stateService.registerProcessors(processors);
        System.out.println("成功注册了 " + count + " 个处理器");
    }
}
```

处理器实现时，确保`getSupportedBizType()`方法返回正确的业务类型：

```java
@Override
public String getSupportedBizType() {
    return "custom_biz";  // 必须与配置中的业务类型一致
}
```

可以通过调用`StateProcessorRegistry`的静态方法查看已注册的处理器：

```java
// 获取所有注册的业务类型
Set<String> supportedTypes = StateProcessorRegistry.getSupportedBizTypes();

// 检查特定业务类型是否已注册
boolean isRegistered = StateProcessorRegistry.getSupportedBizTypes().contains("trade");
```

通过以上步骤，您可以在Spring项目中正确集成和使用状态管理SDK，而无需修改SDK本身的代码或添加Spring依赖。