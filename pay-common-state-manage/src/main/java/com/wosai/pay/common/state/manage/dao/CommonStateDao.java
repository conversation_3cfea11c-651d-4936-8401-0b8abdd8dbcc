package com.wosai.pay.common.state.manage.dao;

import com.wosai.pay.common.data.Jackson2PersistenceHelper;
import com.wosai.pay.common.data.jdbc.JdbcVersionedRecordDao;
import com.wosai.pay.common.state.manage.constant.CommonStateConstant;
import com.wosai.pay.common.state.manage.dao.entity.CommonStateEntity;

import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

public class CommonStateDao extends JdbcVersionedRecordDao<Long, CommonStateEntity> {

    private static final String TABLE_NAME = CommonStateConstant.TABLE_COMMON_STATE;

    public CommonStateDao(NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        super(TABLE_NAME, CommonStateEntity.class, "", namedParameterJdbcTemplate, new Jackson2PersistenceHelper());
    }
}

