package com.wosai.pay.common.state.manage.request;

public abstract class StateChangeBaseBean extends StateBaseBean {

    private Integer type;

    private Boolean open;

    private String remark;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Boolean getOpen() {
        return open;
    }

    public void setOpen(Boolean open) {
        this.open = open;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
