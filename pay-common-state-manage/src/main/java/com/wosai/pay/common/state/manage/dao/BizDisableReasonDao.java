package com.wosai.pay.common.state.manage.dao;


import com.wosai.pay.common.data.Jackson2PersistenceHelper;
import com.wosai.pay.common.data.jdbc.JdbcVersionedRecordDao;
import com.wosai.pay.common.state.manage.constant.CommonStateConstant;
import com.wosai.pay.common.state.manage.dao.entity.bizDisableReasonEntity;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

public class BizDisableReasonDao extends JdbcVersionedRecordDao<Long, bizDisableReasonEntity> {

    private static final String TABLE_NAME = CommonStateConstant.TABLE_BIZ_DISABLE_REASON;

    public BizDisableReasonDao(NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        super(TABLE_NAME, bizDisableReasonEntity.class, "", namedParameterJdbcTemplate, new Jackson2PersistenceHelper());
    }
}

