package com.wosai.pay.common.state.manage.result;

import java.util.List;

public abstract class StateResult {

    /**
     * 状态值 false true , not null
     */
    private Boolean state;

    /**
     * 子状态列表
     */
    private List<SubState> subStateList;
    
    public Boolean getState() {
        return state;
    }

    public void setState(Boolean state) {
        this.state = state;
    }

    public List<SubState> getSubStateList() {
        return subStateList;
    }

    public void setSubStateList(List<SubState> subStateList) {
        this.subStateList = subStateList;
    }

    public static class SubState {
        /**
         * 子状态类型
         */
        private Integer type;
        /**
         * 子状态描述
         */
        private String desc;

        /**
         * 子状态布尔值
         */
        private Boolean value;

        /**
         * 操作备注
         */
        private String remark;
        
        public Integer getType() {
            return type;
        }

        public void setType(Integer type) {
            this.type = type;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }

        public Boolean getValue() {
            return value;
        }

        public void setValue(Boolean value) {
            this.value = value;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }
    }
}
