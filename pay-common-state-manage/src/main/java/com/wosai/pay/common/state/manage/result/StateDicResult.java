package com.wosai.pay.common.state.manage.result;

import java.util.List;

public class StateDicResult {
    /**
     * 业务id
     */
    private String biz;
    /**
     * 描述
     */
    private String desc;

    /**
     * 业务标准化原因列表
     */
    private List<SubStateDic> subStateDicList;


    public StateDicResult(String biz, String desc, List<SubStateDic> subStateDicList) {
        this.biz = biz;
        this.desc = desc;
        this.subStateDicList = subStateDicList;
    }

    public String getBiz() {
        return biz;
    }

    public void setBiz(String biz) {
        this.biz = biz;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public List<SubStateDic> getSubStateDicList() {
        return subStateDicList;
    }

    public void setSubStateDicList(List<SubStateDic> subStateDicList) {
        this.subStateDicList = subStateDicList;
    }

    public static class SubStateDic {
        /**
         * 类型
         */
        private int type;
        /**
         * 描述
         */
        private String desc;

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }

        public SubStateDic(int type, String desc) {
            this.type = type;
            this.desc = desc;
        }
    }
}
