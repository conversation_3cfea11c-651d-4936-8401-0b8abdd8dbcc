package com.wosai.pay.common.state.manage.service.processor;

import com.wosai.pay.common.data.Criteria;
import com.wosai.pay.common.state.manage.dao.entity.CommonStateEntity;
import com.wosai.pay.common.state.manage.request.StateBaseBean;
import com.wosai.pay.common.state.manage.request.StateLogParamsDto;
import com.wosai.pay.common.state.manage.result.StateResult;


/**
 * 状态处理器接口
 * 不同业务类型可以实现此接口来自定义状态处理逻辑
 */
public interface StateProcessor<T extends StateBaseBean, R extends StateResult> {

    /**
     * 获取此处理器支持的实体类型
     * @return 业务类型
     */
    String getSupportedEntityType();


    void generateCriteria(Criteria criteria, T stateBaseBean);


    <E extends CommonStateEntity> E buildCommonStateEntity(T stateBaseBean);


    /**
     * 创建状态查询结果实例
     * @param stateBaseBean 状态查询请求
     * @return 状态查询结果
     */
    R createResultInstance(T stateBaseBean);

    /**
     * 在状态更新后执行的操作
     * @param updateStateBaseBean 状态更新请求
     * @param oldParams 修改前状态
     * @param newParams 修改后状态
     */
     void afterStateUpdate(T updateStateBaseBean, R oldParams, R newParams , StateLogParamsDto stateLogParamsDto);
}