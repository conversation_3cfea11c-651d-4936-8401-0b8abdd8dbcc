package com.wosai.pay.common.state.manage.service;

import com.wosai.pay.common.state.manage.request.StateBaseBean;
import com.wosai.pay.common.state.manage.request.StateChangeBaseBean;
import com.wosai.pay.common.state.manage.request.StateLogParamsDto;
import com.wosai.pay.common.state.manage.result.StateResult;
import com.wosai.pay.common.state.manage.service.processor.StateProcessor;


public interface StateService {

    /**
     * 更新业务的值（记录日志）
     *
     * @param <T> StateChangeBaseBean 的子类类型
     * @param updateStateBaseBean 更新状态请求，必须是 StateChangeBaseBean 的子类
     * @param stateLogParamsDto 操作日志请求
     * @return 是否操作成功
     */
    <T extends StateChangeBaseBean, R extends StateResult> Boolean updateState(T updateStateBaseBean, StateLogParamsDto stateLogParamsDto);

    /**
     * 根据业务查询 状态值
     *
     * @param <T> StateBaseBean 的子类类型
     * @param <R> TradeStateResult 的子类类型
     * @param stateBaseBean 查询状态请求，必须是 StateBaseBean 的子类
     * @return 业务详情，返回 TradeStateResult 的子类
     */
    <T extends StateBaseBean, R extends StateResult> R queryState(T stateBaseBean);

    /**
     * 注册一个状态处理器
     *
     * @param processor 要注册的处理器
     * @return 注册是否成功
     */
    boolean registerProcessor(StateProcessor<?, ?> processor);

}
